import React, { useState, useCallback, useImperativeHandle, forwardRef, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { MultiFieldInput } from '../../MultiFieldInput/MultiFieldInput';
import { MultiFieldInputType, AddressFields } from '../../MultiFieldInput/MultiFieldInput.types';
import styles from './FieldComponents.module.scss';

interface EmailFieldProps {
    initialValue: string;
    onSave?: (value: string) => void;
    onEditStart?: () => void;
    onEditEnd?: () => void;
    onVerificationSuccess?: () => void;
    forceEditing?: boolean;
}

export interface EmailFieldRef {
    startEditing: () => void;
}

export const EmailField = forwardRef<EmailFieldRef, EmailFieldProps>(
    ({ initialValue, onSave, onEditStart, onEditEnd, onVerificationSuccess, forceEditing }, ref) => {
        const { user } = useUser();
        const [isEditing, setIsEditing] = useState(!!forceEditing);
        const [value, setValue] = useState(initialValue);
        const [loading, setLoading] = useState(false);
        const [error, setError] = useState('');

        useEffect(() => {
            if (forceEditing) setIsEditing(true);
        }, [forceEditing]);

        useImperativeHandle(ref, () => ({
            startEditing: () => {
                setValue(initialValue);
                setIsEditing(true);
                setError('');
                onEditStart?.();
            },
        }));

        const handleSendVerificationCode = useCallback(
            async () => {
                if (!user) return false;

                try {
                    // Reload user again to ensure the new email address is available
                    await user.reload();

                    // Find the newly created email address
                    const emailObj = user.emailAddresses[0];
                    if (!emailObj) throw new Error('Email address not found after creation');

                    await emailObj.prepareVerification({ strategy: 'email_code' });
                    return true;
                } catch (err) {
                    console.error('Error sending verification code:', err);
                    setError(err instanceof Error ? err.message : 'Failed to send verification code');
                    return false;
                }
            },
            [user]
        );

        const handleVerifyCode = useCallback(
            async (code: string) => {
                if (!user) return false;

                try {
                    const emailObj = user.emailAddresses[0];
                    if (!emailObj) throw new Error('Email address not found');

                    await emailObj.attemptVerification({ code });
                    await user.reload();

                    const isVerified = emailObj?.verification?.status === 'verified';

                    if (isVerified) {
                        setIsEditing(false);
                        onEditEnd?.();
                        onSave?.(emailObj.emailAddress);
                        onVerificationSuccess?.();
                    }

                    return isVerified;
                } catch (err) {
                    console.error('Error verifying code:', err);
                    setError(err instanceof Error ? err.message : 'Failed to verify code');
                    return false;
                }
            },
            [user, onEditEnd, onSave, onVerificationSuccess]
        );

        const handleSave = useCallback(
            async (inputValue: string | AddressFields) => {
                const email = typeof inputValue === 'string' ? inputValue : value;

                if (!email.trim()) {
                    setError('Please enter an email address');
                    return;
                }

                setLoading(true);
                setError('');

                try {
                    setIsEditing(false);
                    onEditEnd?.();
                    onSave?.(email);
                } catch (err) {
                    const errorMessage = err instanceof Error ? err.message : 'Failed to save email address';
                    setError(errorMessage);
                } finally {
                    setLoading(false);
                }
            },
            [value, onEditEnd, onSave]
        );

        const handleChange = useCallback((newValue: string | AddressFields) => {
            if (typeof newValue === 'string') {
                setValue(newValue);
            }
        }, []);

        if (isEditing) {
            return (
                <div className={styles.editingContainer}>
                    <MultiFieldInput
                        type={MultiFieldInputType.EMAIL}
                        value={value}
                        onChange={handleChange}
                        onSendEmailVerificationCode={handleSendVerificationCode}
                        onVerifyEmailCode={handleVerifyCode}
                        onSave={handleSave}
                        loading={loading}
                        hideSaveButton={true}
                        hideTitle={true}
                        hideBorder={true}
                        placeholder="Enter your email address"
                        disabled={false}
                        containerClassName={styles.noPaddingContainer}
                        error={error}
                    />
                </div>
            );
        }

        return (
            <div className={styles.displayContainer} style={{ width: 'inherit' }}>
                <span className={styles.value}>{initialValue || '—'}</span>
            </div>
        );
    }
);

EmailField.displayName = 'EmailField'; 