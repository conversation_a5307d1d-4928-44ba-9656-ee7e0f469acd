import React, { useState, useCallback, useImperativeHandle, forwardRef } from 'react';
import { useUser } from '@clerk/nextjs';
import { Button } from '../../Button';
import { Input } from '../../Input';
import { ButtonType, ButtonSize } from '../../Button/Button.types';
import { InputState } from '../../Input/Input.types';
import styles from './FieldComponents.module.scss';

interface FirstNameFieldProps {
  initialValue: string;
  onSave?: (value: string) => void;
  onEditStart?: () => void;
  onEditEnd?: () => void;
}

export interface FirstNameFieldRef {
  startEditing: () => void;
}

export const FirstNameField = forwardRef<FirstNameFieldRef, FirstNameFieldProps>(
  ({ initialValue, onSave, onEditStart, onEditEnd }, ref) => {
    const { user } = useUser();
    const [isEditing, setIsEditing] = useState(false);
    const [value, setValue] = useState(initialValue);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    useImperativeHandle(ref, () => ({
      startEditing: () => {
        setValue(initialValue);
        setIsEditing(true);
        setError('');
        onEditStart?.();
      },
    }));

    const handleSave = useCallback(async () => {
      if (!user) return;

      setLoading(true);
      setError('');

      try {
        await user.update({
          firstName: value,
        });

        await user.reload();
        setIsEditing(false);
        onEditEnd?.();
        onSave?.(value);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to update first name';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    }, [user, value, onSave, onEditEnd]);

    if (isEditing) {
      return (
        <div className={styles.editingContainer}>
          <Input
            value={value}
            onChange={setValue}
            placeholderText="First name"
            showLabel={false}
            showHelperText={false}
            showLeftIcon={false}
            state={InputState.NORMAL}
            disableClear={true}
            className={styles.input}
          />
          {error && <div className={styles.error}>{error}</div>}
          <div className={styles.actions + (!error ? ' ' + styles.buttonWithMargin : '')}>
            <Button
              type={ButtonType.PRIMARY}
              size={ButtonSize.BASE}
              onClick={handleSave}
              disabled={loading}
            >
              Save first name
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className={styles.displayContainer}>
        <span className={styles.value}>{initialValue || '—'}</span>
      </div>
    );
  }
);

FirstNameField.displayName = 'FirstNameField';
