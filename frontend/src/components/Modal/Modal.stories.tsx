import React, { useState } from 'react';
import { Meta, StoryFn } from '@storybook/react';
import { Modal } from './Modal';
import { Button } from '../Button';
import { ButtonSize, ButtonType, ButtonColor } from '../Button/Button.types';

const meta = {
  title: 'Components/Modal',
  component: Modal,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    closeButtonPosition: {
      control: 'select',
      options: ['left', 'right'],
      description: 'Position of the close button',
    },
  },
} satisfies Meta<typeof Modal>;

export default meta;

export const Default: StoryFn = () => {
  const [open, setOpen] = useState(false);
  return (
    <div>
      <Button
        onClick={() => setOpen(true)}
        size={ButtonSize.L}
        color={ButtonColor.GREEN_PRIMARY}
        type={ButtonType.PRIMARY}
      >
        Open Modal
      </Button>
      <Modal
        open={open}
        onClose={() => setOpen(false)}
        title="Default Modal"
      >
        <p>This is the default modal with close button on the right.</p>
        <p>The title should not overlap with the close button.</p>
      </Modal>
    </div>
  );
};

export const WithLeftCloseButton: StoryFn = () => {
  const [open, setOpen] = useState(false);
  return (
    <div>
      <Button
        onClick={() => setOpen(true)}
        size={ButtonSize.L}
        color={ButtonColor.GREEN_PRIMARY}
        type={ButtonType.PRIMARY}
      >
        Open Modal (Left Close)
      </Button>
      <Modal
        open={open}
        onClose={() => setOpen(false)}
        title="Modal with Left Close Button"
        closeButtonPosition="left"
      >
        <p>This modal has the close button on the left.</p>
        <p>The title should still not overlap with the close button.</p>
      </Modal>
    </div>
  );
};

export const WithActionButtons: StoryFn = () => {
  const [open, setOpen] = useState(false);
  return (
    <div>
      <Button
        onClick={() => setOpen(true)}
        size={ButtonSize.L}
        color={ButtonColor.GREEN_PRIMARY}
        type={ButtonType.PRIMARY}
      >
        Open Modal with Actions
      </Button>
      <Modal
        open={open}
        onClose={() => setOpen(false)}
        title="Modal with Action Buttons"
        actionButtons={
          <div style={{ display: 'flex', gap: '12px' }}>
            <Button
              onClick={() => setOpen(false)}
              size={ButtonSize.L}
              color={ButtonColor.GREEN_PRIMARY}
              type={ButtonType.SECONDARY}
            >
              Cancel
            </Button>
            <Button
              onClick={() => setOpen(false)}
              size={ButtonSize.L}
              color={ButtonColor.GREEN_PRIMARY}
              type={ButtonType.PRIMARY}
            >
              Save
            </Button>
          </div>
        }
      >
        <p>This modal has action buttons at the bottom.</p>
        <p>The close button is positioned on the right by default.</p>
      </Modal>
    </div>
  );
};

export const LongContent: StoryFn = () => {
  const [open, setOpen] = useState(false);
  return (
    <div>
      <Button
        onClick={() => setOpen(true)}
        size={ButtonSize.L}
        color={ButtonColor.GREEN_PRIMARY}
        type={ButtonType.PRIMARY}
      >
        Open Modal with Long Content
      </Button>
      <Modal
        open={open}
        onClose={() => setOpen(false)}
        title="Modal with Very Long Title That Should Not Overlap with Close Button"
      >
        <div>
          <p>This modal has a very long title to test the spacing.</p>
          <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
          <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
          <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
          <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
          <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.</p>
          <p>Totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>
          <p>Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit.</p>
          <p>Sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.</p>
          <p>Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit.</p>
        </div>
      </Modal>
    </div>
  );
};
