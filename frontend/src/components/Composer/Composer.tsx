import React, { KeyboardEvent, useEffect, useRef, useState } from 'react';
import classNames from 'classnames';
import { Button } from '@/components/Button';
import { ButtonColor, ButtonSize, ButtonState, ButtonType } from '@/components/Button/Button.types';
import styles from './Composer.module.scss';
import { ComposerProps, ComposerSize, ControlState } from './Composer.types';
import { GlobalDropZone } from './components/GlobalDropZone/GlobalDropZone';
import { FileUploadGrid } from '@/components/FileUploadManager';
import { Attachment01Icon, IconSvgObject, Mic02Icon, TelegramIcon } from '@hugeicons/react';
import { debounce } from 'lodash';
import { useChats } from '@/hooks/useChats';
import { Spinner } from '@/components/Spinner/Spinner';
import { useAuth } from '@clerk/nextjs';
import { Attachment } from '@/types/messages';
import { useBreakpoint } from '@/utils/breakpointUtils';
import { SquareLock01Icon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { useSWRDocumentUpload } from '@/hooks/useSWRDocumentUpload';
import { useMessages } from '@/hooks/useMessages';
import useMessageSender from '@/hooks/useMessageSender';
import { UniversalFile } from '@/types/file';
import { ALL_SUPPORTED_TYPES, getFileType, isValidFile } from '@/utils/fileUtils';
import { deleteDocument } from '@/api/documents';
import { useModalForGuest } from '@/hooks/useModalGuest';
import { useSearchParams } from 'next/navigation';

interface SpeechRecognitionResult {
  transcript: string;
}

interface SpeechRecognitionEvent {
  results: [[SpeechRecognitionResult]];
}

interface SpeechRecognition {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  onresult: (event: SpeechRecognitionEvent) => void;
  onend: () => void;
  start: () => void;
  stop: () => void;
}

declare global {
  interface Window {
    webkitSpeechRecognition: new () => SpeechRecognition;
    clipboardData: DataTransfer;
  }
}

export const Composer = ({
  className,
  placeholder = 'Message Alfie',
  size = ComposerSize.DEFAULT,
  defaultValue = '',
  isRecording = false,
  controlState = ControlState.ACTIVE,
  disabled = false,
  isLocked = false,
}: ComposerProps): JSX.Element => {
  const inputRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [isRecordingState, setIsRecording] = useState(isRecording);
  const { isSignedIn } = useAuth();
  const searchParams = useSearchParams();
  const queryParam = searchParams?.get('query') || '';

  const initialValue = defaultValue || (!isSignedIn && queryParam ? queryParam : '');
  const [value, setValue] = useState(initialValue);

  const [isDragging, setIsDragging] = useState(false);
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);
  const { sendMessage } = useMessageSender();
  const { isSubmitting } = useChats();
  const { getToken } = useAuth();
  const { isDesktop } = useBreakpoint();
  const { uploadDocument: swrUploadDocument } = useSWRDocumentUpload('chat');
  const { jobSummaryConfirmed } = useMessages();
  const { openModal } = useModalForGuest();

  const debouncedSetIsDragging = debounce((value: boolean) => {
    setIsDragging(value);
  }, 100);

  useEffect(() => {
    if (inputRef.current && defaultValue) {
      inputRef.current.innerHTML = defaultValue;
      setValue(defaultValue);
    }
  }, [defaultValue]);

  useEffect(() => {
    return () => {
      if (recognition) {
        recognition.onend = () => {};
        recognition.onresult = () => {};
      }
    };
  }, [recognition]);

  useEffect(() => {
    isDesktop && inputRef.current?.focus();
  }, [isDesktop]);

  useEffect(() => {
    if (!isSignedIn && queryParam && !defaultValue) {
      setValue(queryParam);
      if (inputRef.current) {
        inputRef.current.textContent = queryParam;
      }
    }
  }, [queryParam, isSignedIn, defaultValue]);

  const handleKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleInput = (event: React.FormEvent<HTMLDivElement>) => {
    const newContent = event.currentTarget.textContent || '';
    setValue(newContent);
  };

  const handleSend = async () => {
    if (!isSignedIn) {
      const currentUrl = window.location.href;
      const url = new URL(currentUrl);

      if (value.trim()) {
        url.searchParams.set('query', value.trim());
      }

      openModal(url.toString());
      return;
    }
    if (!value.trim() && attachments.length === 0) return;

    setValue('');

    if (inputRef?.current) {
      inputRef.current.innerHTML = '';
    }

    const attachmentsList = attachments.map((attachment) => ({
      documentId: Number(attachment.documentId),
      name: attachment.name,
      type: attachment.type,
      size: attachment.size,
      file: attachment.file,
      status: attachment.status,
      progress: attachment.progress,
      createdAt: attachment.createdAt || new Date().toISOString(),
      fileExtension: attachment.file?.type.split('/')[1],
      originalFileName: attachment.file?.name,
      sizeInKiloBytes: attachment.file?.size,
      fileId: attachment.fileId,
      cdnUrl: attachment.cdnUrl,
    }));

    setAttachments([]);
    await sendMessage(value, attachmentsList);
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const text = (e.clipboardData || window.clipboardData).getData('text/plain');
    const selection = window.getSelection();
    const range = selection?.getRangeAt(0);

    if (range && inputRef.current) {
      range.deleteContents();
      const textNode = document.createTextNode(text);
      range.insertNode(textNode);
      range.setStartAfter(textNode);
      range.setEndAfter(textNode);
      selection?.removeAllRanges();
      selection?.addRange(range);

      setValue(inputRef.current.textContent || '');
    }
  };

  const handleFileSelect = () => {
    if (!isSignedIn) {
      openModal();
      return;
    }
    fileInputRef.current?.click();
  };

  const handleFileUpload = async (file: File) => {
    if (!isValidFile(file)) {
      console.error('Unsupported file type:', file.type);
      return;
    }

    const fileId = Math.floor(Math.random() * 10000);

    setAttachments((prev) => [
      ...prev,
      {
        documentId: fileId,
        name: file.name,
        type: getFileType(file),
        size: file.size,
        file: file,
        status: 'uploading',
        progress: 0,
        createdAt: new Date().toISOString(),
        fileExtension: file.type.split('/')[1],
        originalFileName: file.name,
        sizeInKiloBytes: file.size,
      },
    ]);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const token = await getToken();
      if (token) {
        const response = await swrUploadDocument({
          formData,
          token,
        });

        setAttachments((prev) =>
          prev.map((attachment) => {
            if (attachment.documentId === fileId) {
              return {
                ...attachment,
                status: 'success',
                documentId: response.id,
                fileId: String(response.id),
              };
            }
            return attachment;
          })
        );
      }
    } catch (error) {
      console.error('Upload failed:', error);
      setAttachments((prev) =>
        prev.map((attachment) =>
          attachment.documentId === fileId
            ? {
                ...attachment,
                status: 'error',
              }
            : attachment
        )
      );
    }
  };

  const handleRemoveFile = async (fileId: number) => {
    try {
      const token = await getToken();
      token && (await deleteDocument(token, fileId));
    } catch (error) {
      console.error('Failed to delete document:', error);
    }
    setAttachments((prev) => prev.filter((file) => file.documentId !== fileId));
  };

  const handleRetry = async (file: Attachment) => {
    setAttachments((prev) =>
      prev.map((attachment) =>
        attachment.documentId === file.documentId
          ? {
              ...attachment,
              status: 'uploading',
            }
          : attachment
      )
    );
  };

  const stopVoiceRecording = () => {
    if (recognition) {
      recognition.onend = () => {
        setIsRecording(false);
        setRecognition(null);
      };
      recognition.stop();
    }
  };

  const startVoiceRecording = () => {
    if ('webkitSpeechRecognition' in window) {
      if (isRecordingState) {
        stopVoiceRecording();
        return;
      }

      const newRecognition = new window.webkitSpeechRecognition();
      newRecognition.continuous = true;
      newRecognition.interimResults = false;
      newRecognition.lang = 'en-US';

      newRecognition.onresult = (event: SpeechRecognitionEvent) => {
        const lastResultIndex = event.results.length - 1;
        const transcript = event.results[lastResultIndex][0].transcript;
        if (inputRef.current) {
          const currentText = inputRef.current.textContent || '';
          const newText = currentText + transcript;
          inputRef.current.textContent = newText;
          setValue(newText);
        }
      };

      newRecognition.onend = () => {
        setIsRecording(false);
        setRecognition(null);
        isDesktop && inputRef.current?.focus();
        const range = document.createRange();
        const selection = window.getSelection();
        if (inputRef.current && selection) {
          range.selectNodeContents(inputRef.current);
          range.collapse(false);
          selection.removeAllRanges();
          selection.addRange(range);
        }
      };

      setIsRecording(true);
      setRecognition(newRecognition);
      newRecognition.start();
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newFiles = event.target.files;
    if (!newFiles?.length) return;

    const filesArray = Array.from(newFiles);
    filesArray.forEach((file) => handleFileUpload(file));

    event.target.value = '';
  };

  return (
    <div
      className={styles.Maincontainer}
      onDragEnter={
        !disabled
          ? (e: React.DragEvent) => {
              e.preventDefault();
              e.stopPropagation();
              debouncedSetIsDragging(true);
            }
          : undefined
      }
      onDragLeave={
        !disabled
          ? (e: React.DragEvent) => {
              e.preventDefault();
              e.stopPropagation();
              if (!e.currentTarget.contains(e.relatedTarget as Node)) {
                debouncedSetIsDragging(false);
              }
            }
          : () => {
              debouncedSetIsDragging(false);
            }
      }
      onDrop={
        !disabled
          ? (e: React.DragEvent) => {
              e.preventDefault();
              e.stopPropagation();
              debouncedSetIsDragging(false);
              const files = Array.from(e.dataTransfer.files);
              files.forEach((file) => handleFileUpload(file));
            }
          : undefined
      }
      onDragOver={
        !disabled
          ? (e: React.DragEvent) => {
              e.preventDefault();
              e.stopPropagation();
              debouncedSetIsDragging(false);
            }
          : undefined
      }
    >
      {isLocked && jobSummaryConfirmed && (
        <div className={styles.lockedOverlay}>
          <div>
            <HugeiconsIcon
              icon={SquareLock01Icon as unknown as IconSvgObject}
              size={16}
              color="#A34E22"
            />
          </div>
          <span>This chat is locked</span>
        </div>
      )}
      {isDragging && !disabled && <GlobalDropZone onFileUpload={handleFileUpload} />}
      <div className={styles.container}>
        <div
          className={classNames(styles.composer, styles[size], className, {
            [styles.disabled]: disabled,
          })}
        >
          <div
            ref={inputRef}
            className={classNames(styles.input, {
              [styles.disabled]: disabled || controlState === ControlState.INACTIVE || isLocked,
              [styles.hasAttachments]: attachments.length > 0,
              [styles.empty]: !value.trim(),
            })}
            contentEditable={!disabled && controlState !== ControlState.INACTIVE && !isLocked}
            onInput={!disabled ? handleInput : undefined}
            onKeyDown={!disabled ? handleKeyDown : undefined}
            onPaste={!disabled ? handlePaste : undefined}
            data-placeholder={placeholder}
            role="textbox"
            aria-multiline="true"
            aria-label={placeholder}
          />
          {attachments.length > 0 && (
            <div className={styles.attachments}>
              <FileUploadGrid
                files={attachments.map((attachment) => ({
                  ...attachment,
                  id: String(attachment.documentId),
                }))}
                onFileRemove={
                  !disabled
                    ? (fileId: string | number) => handleRemoveFile(fileId as number)
                    : undefined
                }
                onFileRetry={
                  !disabled
                    ? (file: UniversalFile) => {
                        if ('documentId' in file && typeof file.documentId === 'number') {
                          handleRetry(file as Attachment);
                        }
                      }
                    : undefined
                }
                showRemoveButton={!disabled}
              />
            </div>
          )}
          <div
            className={classNames(styles.controls, {
              [styles.disabled]: disabled,
            })}
          >
            <div className={styles.leftControls}>
              <Button
                type={ButtonType.TERTIARY}
                size={ButtonSize.XS}
                color={ButtonColor.GREEN_PRIMARY}
                iconOnly
                leftIcon={<Attachment01Icon />}
                showLeftIcon
                onClick={!disabled && !isLocked ? handleFileSelect : undefined}
                aria-label="Attach file"
                state={disabled || isLocked ? ButtonState.DISABLED : ButtonState.DEFAULT}
              />
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept={ALL_SUPPORTED_TYPES}
                className={styles.hiddenInput}
                data-testid="file-input"
                onChange={!disabled && !isLocked ? handleFileChange : undefined}
                disabled={disabled || isLocked}
              />
              {/* <Button
                type={ButtonType.TERTIARY}
                size={ButtonSize.XS}
                color={ButtonColor.GREEN_PRIMARY}
                iconOnly
                leftIcon={<Attachment01Icon />}
                showLeftIcon
                aria-label="Insert link"
                state={disabled ? ButtonState.DISABLED : ButtonState.DEFAULT}
              /> */}
            </div>
            <div className={styles.rightControls}>
              <Button
                type={ButtonType.TERTIARY}
                size={ButtonSize.XS}
                color={ButtonColor.GREEN_PRIMARY}
                iconOnly
                leftIcon={<Mic02Icon />}
                showLeftIcon
                onClick={!disabled && !isLocked ? startVoiceRecording : undefined}
                aria-label="Voice to text"
                className={classNames({
                  [styles.recording]: isRecordingState,
                })}
                state={disabled || isLocked ? ButtonState.DISABLED : ButtonState.DEFAULT}
              />
              <Button
                type={ButtonType.PRIMARY}
                size={ButtonSize.XS}
                iconOnly
                leftIcon={
                  isSubmitting ? <Spinner data-testid="loading-spinner" /> : <TelegramIcon />
                }
                showLeftIcon
                onClick={!disabled ? handleSend : undefined}
                aria-label="Send message"
                state={
                  (disabled || (!value.trim() && attachments.length === 0)) && !isSubmitting
                    ? ButtonState.DISABLED
                    : ButtonState.DEFAULT
                }
                className={classNames(styles.sendButton, {
                  [styles.loading]: isSubmitting,
                })}
                loading={isSubmitting}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
