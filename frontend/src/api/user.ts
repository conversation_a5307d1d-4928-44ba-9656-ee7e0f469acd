'use client';

import axios from 'axios';
import { getApiUrl, API_ENDPOINTS } from '@/constants/api';

export enum UserPropertyRelationType {
  OwnerAndOccupier = 'ownerAndOccupier',
  Landlord = 'landlord',
  Tenant = 'tenant',
  ManagingProfessional = 'managingProfessional',
}

export interface UserDetails {
  id: number;
  clerkId: string;
  email: string;
  firstName: string;
  lastName: string | null;
  phoneNumber: string | null;
  canUserAcceptJobs: boolean;
}

export interface UserUpdate {
  mainUsage: UserPropertyRelationType;
}

export async function getCurrentUser({ token }: { token?: string | null }): Promise<UserDetails> {
  const response = await axios.get<UserDetails>(getApiUrl(`${API_ENDPOINTS.USER}/`), {
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
    },
  });

  if (!response.data) {
    throw new Error('Failed to fetch user details');
  }

  return response.data;
}

export async function updateUserDetails({
  request,
  token,
}: {
  request: UserUpdate;
  token?: string | null;
}): Promise<UserDetails> {
  const response = await axios.patch<UserDetails>(getApiUrl(`${API_ENDPOINTS.USER}/`), request, {
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
  });

  if (!response.data) {
    throw new Error('Failed to update user details');
  }

  return response.data;
}
