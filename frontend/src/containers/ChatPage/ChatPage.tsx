'use client';

import React, { useEffect, useMemo, useRef } from 'react';
import { Composer } from '@/components/Composer';
import { Messages } from '@/components/Messages';
import { useMessages } from '@/hooks/useMessages';
import styles from './ChatPage.module.scss';
import { ChatPageProps } from './ChatPage.types';
import { useChats } from '@/hooks/useChats';
import { useAuth } from '@clerk/nextjs';
import classNames from 'classnames';
import useChatParams from '@/hooks/useChatParams';
import useMessageSender from '@/hooks/useMessageSender';
import { useGetEntityLink } from '@/hooks/useEntityLinkService';
import { CheckListIcon } from '@hugeicons-pro/core-solid-standard';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { IconSvgObject } from '@hugeicons/react';

export const ChatPage: React.FC<ChatPageProps> = ({ chatId }) => {
  const { isLoading, hasMore, fetchMessages, loadMoreMessages } = useMessages();
  const { isSubmitting, optimisticMessage, chats } = useChats();
  const anchorRef = useRef<HTMLDivElement>(null);
  const { getToken } = useAuth();
  const messages = useMemo(
    () => chats.find((chat) => chat.id === chatId)?.messages || [],
    [chats, chatId]
  );
  const isChatLocked = !!messages[0]?.additionalData?.jobSummary?.jobId;
  const { query } = useChatParams();
  const { sendMessage } = useMessageSender();

  useEffect(() => {
    if (chatId && messages.length === 0) {
      getToken().then((token) => {
        if (token) {
          fetchMessages(chatId, token).then(() => console.log('messages fetched'));
        }
      });
    }
  }, [chatId, fetchMessages, getToken, messages.length, chats]);

  useEffect(() => {
    if (!chatId && query) {
      sendMessage(query).then(() => console.log('message sent'));
    }
  }, [chatId, query, sendMessage]);

  const { data: entityLinks } = useGetEntityLink({
    entityId: chatId ?? -1,
    entityType: 'chats',
  });

  const firstTodoEntityType = useMemo(() => {
    return entityLinks?.filter((link) => link.entityType === 'todos')[0];
  }, [entityLinks]);

  return (
    <div className={styles.chatContainer}>
      {firstTodoEntityType?.entity.name && (
        <div
          className="absolute top-[64px] left-0 w-full p-2 bg-neutral-10 z-40 border border-neutral-100"
          ref={anchorRef}
        >
          <a
            href={`/todo-list?todo=${firstTodoEntityType.entity.id}`}
            className="text-sm text-neutral-800 flex items-center gap-1"
          >
            <HugeiconsIcon icon={CheckListIcon as unknown as IconSvgObject} size={16} />
            Linked to to-do: {firstTodoEntityType.entity.name}
          </a>
        </div>
      )}
      <Messages
        optimisticMessage={optimisticMessage}
        anchorRef={anchorRef}
        messages={messages}
        isLoading={isLoading}
        isSubmitting={isSubmitting}
        hasMore={hasMore}
        onLoadMore={async () => {
          const token = await getToken();
          if (token && chatId) {
            loadMoreMessages(chatId, token).then(() => console.log('more messages loaded'));
          }
        }}
      />
      <div
        className={classNames(styles.composerContainer, {
          [styles.locked]: isChatLocked,
        })}
      >
        <Composer anchorRef={anchorRef} isLocked={isChatLocked} />
      </div>
    </div>
  );
};
